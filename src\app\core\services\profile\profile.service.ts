import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ProfileData,
  PracticeArea,
  Skill,
  IndustryExperience,
  NotableMatter,
} from '../../models/profile.model';

@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  private profiles: ProfileData[] = [
    {
      id: 1,
      name: '<PERSON>',
      initials: 'S<PERSON>',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'New York',
      yearsExperience: 5,
      yearsWithFirm: 5,
      practiceAreas: [
        { name: 'Corporate Law', level: 'Expert' },
        { name: 'Mergers & Acquisitions', level: 'Advanced' },
        { name: 'Technology Transactions', level: 'Intermediate' },
        { name: 'Intellectual Property', level: 'Beginner' },
      ],
      skills: [
        { name: 'Private Equity' },
        { name: 'Venture Capital' },
        { name: 'Public M&A' },
        { name: 'Cross-border M&A' },
        { name: 'Joint Ventures' },
        { name: 'Corporate Restructuring' },
        { name: 'IPOs' },
        { name: 'Debt Financing' },
        { name: 'Asset Sales' },
        { name: 'Stock Purchase' },
        { name: 'Due Diligence' },
        { name: 'Deal Structuring' },
        { name: 'Strategic Alliances' },
        { name: 'Corporate Governance' },
        { name: 'Securities Regulation' },
        { name: 'Contract Negotiation' },
        { name: 'Regulatory Compliance' },
        { name: 'International Trade' },
        { name: 'Technology Licensing' },
        { name: 'Intellectual Property' },
        { name: 'Data Privacy' },
        { name: 'Employment Law' },
        { name: 'Environmental Law' },
        { name: 'Real Estate' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Healthcare' },
        { name: 'Financial Services' },
        { name: 'Manufacturing' },
        { name: 'Retail' },
        { name: 'Energy' },
        { name: 'Transportation' },
        { name: 'Media & Entertainment' },
        { name: 'Telecommunications' },
        { name: 'Real Estate' },
        { name: 'Consumer Goods' },
        { name: 'Pharmaceuticals' },
      ],
      professionalSummary:
        'Corporate attorney with extensive experience in M&A transactions and private equity deals. Specialized in technology and healthcare sectors.',
      education: [
        {
          degree: 'J.D.',
          institution: 'Harvard Law School',
        },
        {
          degree: 'B.A.',
          institution: 'Yale University',
        },
      ],
      barAdmissions: [{ state: 'New York' }, { state: 'California' }],
      notableMatters: [
        {
          title: 'Cross-border acquisition of tech startup',
          description: 'Led the legal team in $500M acquisition deal',
          year: '2024',
          role: 'Lead Associate',
        },
        {
          title: 'Major healthcare merger',
          description: 'Managed due diligence and regulatory compliance',
          year: '2023',
          role: 'Senior Associate',
        },
        {
          title: 'International joint venture',
          description: 'Structured and negotiated $1B joint venture agreement',
          year: '2023',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 95,
      },
    },
    {
      id: 2,
      name: 'Michael Chen',
      initials: 'MC',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'San Francisco',
      yearsExperience: 7,
      yearsWithFirm: 4,
      practiceAreas: [
        { name: 'Intellectual Property', level: 'Expert' },
        { name: 'Technology Transactions', level: 'Advanced' },
      ],
      skills: [
        { name: 'Patent Prosecution' },
        { name: 'Patent Litigation' },
        { name: 'Trademark Registration' },
        { name: 'Copyright Law' },
        { name: 'IP Due Diligence' },
        { name: 'Technology Licensing' },
        { name: 'Software Licensing' },
        { name: 'Open Source Compliance' },
        { name: 'Data Privacy' },
        { name: 'Trade Secrets' },
        { name: 'IP Portfolio Management' },
        { name: 'IP Strategy' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Software' },
        { name: 'Biotechnology' },
        { name: 'Pharmaceuticals' },
        { name: 'Medical Devices' },
        { name: 'Telecommunications' },
        { name: 'Consumer Electronics' },
      ],
      professionalSummary:
        'Intellectual property attorney specializing in technology and life sciences. Experienced in patent prosecution, IP litigation, and technology transactions.',
      education: [
        {
          degree: 'J.D.',
          institution: 'Stanford Law School',
        },
        {
          degree: 'B.S. Computer Science',
          institution: 'MIT',
        },
      ],
      barAdmissions: [
        { state: 'California' },
        { state: 'New York' },
        { state: 'USPTO' },
      ],
      notableMatters: [
        {
          title: 'Patent litigation for tech giant',
          description:
            'Successfully defended client in $200M patent infringement case',
          year: '2023',
          role: 'Lead Associate',
        },
        {
          title: 'IP due diligence for acquisition',
          description:
            'Conducted comprehensive IP review for $1.5B acquisition',
          year: '2022',
          role: 'Associate',
        },
        {
          title: 'Global licensing program',
          description:
            'Developed and implemented global technology licensing strategy',
          year: '2021',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 88,
      },
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      initials: 'ER',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Chicago',
      yearsExperience: 12,
      yearsWithFirm: 8,
      practiceAreas: [
        { name: 'Litigation', level: 'Expert' },
        { name: 'Dispute Resolution', level: 'Expert' },
        { name: 'Regulatory Investigations', level: 'Advanced' },
      ],
      skills: [
        { name: 'Commercial Litigation' },
        { name: 'Class Action Defense' },
        { name: 'Securities Litigation' },
        { name: 'Antitrust Litigation' },
        { name: 'White Collar Defense' },
        { name: 'Internal Investigations' },
        { name: 'Regulatory Enforcement' },
        { name: 'Arbitration' },
        { name: 'Mediation' },
        { name: 'Trial Advocacy' },
        { name: 'Appellate Practice' },
      ],
      industryExperience: [
        { name: 'Financial Services' },
        { name: 'Banking' },
        { name: 'Insurance' },
        { name: 'Healthcare' },
        { name: 'Pharmaceuticals' },
        { name: 'Energy' },
        { name: 'Technology' },
      ],
      professionalSummary:
        'Experienced litigator with a focus on complex commercial disputes, securities litigation, and regulatory investigations. Represents clients in high-stakes matters across various industries.',
      education: [
        {
          degree: 'J.D.',
          institution: 'Columbia Law School',
        },
        {
          degree: 'B.A.',
          institution: 'University of Chicago',
        },
      ],
      barAdmissions: [
        { state: 'Illinois' },
        { state: 'New York' },
        { state: 'California' },
      ],
      notableMatters: [
        {
          title: 'Securities class action defense',
          description:
            'Successfully defended financial institution in $500M class action',
          year: '2023',
          role: 'Lead Partner',
        },
        {
          title: 'Regulatory investigation',
          description:
            'Represented healthcare company in multi-agency investigation',
          year: '2022',
          role: 'Partner',
        },
        {
          title: 'Commercial arbitration',
          description: 'Won $300M arbitration award for energy company client',
          year: '2021',
          role: 'Partner',
        },
      ],
      availability: {
        status: 'Available next month',
        matchPercentage: 75,
      },
    },
    {
      id: 4,
      name: 'David Kim',
      initials: 'DK',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'New York',
      yearsExperience: 8,
      yearsWithFirm: 6,
      practiceAreas: [
        { name: 'Corporate Law', level: 'Expert' },
        { name: 'Mergers & Acquisitions', level: 'Expert' },
        { name: 'Securities Law', level: 'Advanced' },
      ],
      skills: [
        { name: 'M&A Transactions' },
        { name: 'Due Diligence' },
        { name: 'Corporate Restructuring' },
        { name: 'Private Equity' },
        { name: 'Public M&A' },
        { name: 'Cross-border M&A' },
        { name: 'Joint Ventures' },
        { name: 'Securities Offerings' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Financial Services' },
        { name: 'Healthcare' },
      ],
      professionalSummary:
        'Corporate attorney specializing in M&A transactions and securities law.',
      education: [
        { degree: 'J.D.', institution: 'Columbia Law School' },
        { degree: 'B.A. Economics', institution: 'University of Pennsylvania' },
      ],
      barAdmissions: [{ state: 'New York' }, { state: 'New Jersey' }],
      notableMatters: [
        {
          title: 'Technology company merger',
          description: 'Advised on $2B merger transaction',
          year: '2024',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 90,
      },
    },
    {
      id: 5,
      name: 'Lisa Wang',
      initials: 'LW',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'San Francisco',
      yearsExperience: 4,
      yearsWithFirm: 3,
      practiceAreas: [
        { name: 'Corporate Law', level: 'Advanced' },
        { name: 'Mergers & Acquisitions', level: 'Intermediate' },
      ],
      skills: [
        { name: 'M&A Due Diligence' },
        { name: 'Corporate Finance' },
        { name: 'Venture Capital' },
        { name: 'Startup Acquisitions' },
        { name: 'Technology Transactions' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Software' },
        { name: 'Startups' },
      ],
      professionalSummary:
        'Corporate associate focusing on M&A and venture capital transactions in the technology sector.',
      education: [
        { degree: 'J.D.', institution: 'UC Berkeley Law' },
        { degree: 'B.S. Computer Science', institution: 'Stanford University' },
      ],
      barAdmissions: [{ state: 'California' }],
      notableMatters: [
        {
          title: 'Startup acquisition',
          description: 'Managed acquisition of AI startup',
          year: '2024',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 85,
      },
    },
    {
      id: 6,
      name: 'Robert Thompson',
      initials: 'RT',
      title: 'Senior Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Boston',
      yearsExperience: 15,
      yearsWithFirm: 12,
      practiceAreas: [
        { name: 'Employment Law', level: 'Expert' },
        { name: 'Labor Relations', level: 'Expert' },
        { name: 'Workplace Investigations', level: 'Advanced' },
      ],
      skills: [
        { name: 'Employment Litigation' },
        { name: 'Wage and Hour Compliance' },
        { name: 'Discrimination Claims' },
        { name: 'Executive Compensation' },
        { name: 'Union Negotiations' },
        { name: 'OSHA Compliance' },
      ],
      industryExperience: [
        { name: 'Manufacturing' },
        { name: 'Healthcare' },
        { name: 'Retail' },
      ],
      professionalSummary:
        'Employment law expert with extensive experience in labor relations and workplace compliance.',
      education: [
        { degree: 'J.D.', institution: 'Boston University Law School' },
        { degree: 'B.A. Political Science', institution: 'Harvard University' },
      ],
      barAdmissions: [{ state: 'Massachusetts' }, { state: 'New York' }],
      notableMatters: [
        {
          title: 'Major employment class action defense',
          description: 'Successfully defended Fortune 500 company',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 92,
      },
    },
    {
      id: 7,
      name: 'Jennifer Martinez',
      initials: 'JM',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Miami',
      yearsExperience: 6,
      yearsWithFirm: 4,
      practiceAreas: [
        { name: 'Real Estate', level: 'Advanced' },
        { name: 'Construction Law', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Commercial Real Estate' },
        { name: 'Residential Transactions' },
        { name: 'Zoning and Land Use' },
        { name: 'Construction Contracts' },
        { name: 'Property Development' },
      ],
      industryExperience: [
        { name: 'Real Estate' },
        { name: 'Construction' },
        { name: 'Hospitality' },
      ],
      professionalSummary:
        'Real estate attorney specializing in commercial transactions and construction law.',
      education: [
        { degree: 'J.D.', institution: 'University of Miami Law School' },
        {
          degree: 'B.B.A. Finance',
          institution: 'Florida International University',
        },
      ],
      barAdmissions: [{ state: 'Florida' }],
      notableMatters: [
        {
          title: 'Mixed-use development project',
          description: 'Managed legal aspects of $100M development',
          year: '2023',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available next month',
        matchPercentage: 78,
      },
    },
    {
      id: 8,
      name: 'Andrew Wilson',
      initials: 'AW',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Seattle',
      yearsExperience: 18,
      yearsWithFirm: 15,
      practiceAreas: [
        { name: 'Environmental Law', level: 'Expert' },
        { name: 'Regulatory Compliance', level: 'Expert' },
        { name: 'Energy Law', level: 'Advanced' },
      ],
      skills: [
        { name: 'Environmental Compliance' },
        { name: 'Clean Air Act' },
        { name: 'Water Rights' },
        { name: 'Renewable Energy' },
        { name: 'Climate Change Law' },
        { name: 'Environmental Impact Assessment' },
      ],
      industryExperience: [
        { name: 'Energy' },
        { name: 'Manufacturing' },
        { name: 'Mining' },
        { name: 'Utilities' },
      ],
      professionalSummary:
        'Environmental law expert with focus on renewable energy and regulatory compliance.',
      education: [
        { degree: 'J.D.', institution: 'University of Washington Law School' },
        {
          degree: 'M.S. Environmental Science',
          institution: 'University of Washington',
        },
      ],
      barAdmissions: [{ state: 'Washington' }, { state: 'Oregon' }],
      notableMatters: [
        {
          title: 'Wind farm development',
          description: 'Led environmental permitting for major wind project',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 88,
      },
    },
    {
      id: 9,
      name: 'Rachel Green',
      initials: 'RG',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Houston',
      yearsExperience: 9,
      yearsWithFirm: 7,
      practiceAreas: [
        { name: 'Tax Law', level: 'Expert' },
        { name: 'Corporate Tax', level: 'Advanced' },
        { name: 'International Tax', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Tax Planning' },
        { name: 'Tax Controversy' },
        { name: 'Transfer Pricing' },
        { name: 'International Tax Compliance' },
        { name: 'State and Local Tax' },
        { name: 'Tax-Efficient Structures' },
      ],
      industryExperience: [
        { name: 'Oil and Gas' },
        { name: 'Technology' },
        { name: 'Financial Services' },
      ],
      professionalSummary:
        'Tax attorney with expertise in corporate and international tax matters.',
      education: [
        { degree: 'J.D.', institution: 'University of Texas Law School' },
        { degree: 'LL.M. Taxation', institution: 'New York University' },
      ],
      barAdmissions: [{ state: 'Texas' }, { state: 'New York' }],
      notableMatters: [
        {
          title: 'International tax restructuring',
          description: 'Advised multinational on tax-efficient structure',
          year: '2024',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 91,
      },
    },
    {
      id: 10,
      name: 'James Anderson',
      initials: 'JA',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Atlanta',
      yearsExperience: 5,
      yearsWithFirm: 3,
      practiceAreas: [
        { name: 'Healthcare Law', level: 'Advanced' },
        { name: 'Regulatory Compliance', level: 'Intermediate' },
      ],
      skills: [
        { name: 'HIPAA Compliance' },
        { name: 'Healthcare Transactions' },
        { name: 'FDA Regulations' },
        { name: 'Medical Device Law' },
        { name: 'Telemedicine' },
        { name: 'Healthcare Privacy' },
      ],
      industryExperience: [
        { name: 'Healthcare' },
        { name: 'Pharmaceuticals' },
        { name: 'Medical Devices' },
      ],
      professionalSummary:
        'Healthcare attorney specializing in regulatory compliance and transactions.',
      education: [
        { degree: 'J.D.', institution: 'Emory University Law School' },
        { degree: 'B.S. Biology', institution: 'Georgia Tech' },
      ],
      barAdmissions: [{ state: 'Georgia' }],
      notableMatters: [
        {
          title: 'Hospital system merger',
          description: 'Managed regulatory aspects of healthcare merger',
          year: '2023',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 83,
      },
    },
    {
      id: 11,
      name: 'Michelle Davis',
      initials: 'MD',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Dallas',
      yearsExperience: 14,
      yearsWithFirm: 10,
      practiceAreas: [
        { name: 'Banking and Finance', level: 'Expert' },
        { name: 'Securities Law', level: 'Advanced' },
        { name: 'Mergers & Acquisitions', level: 'Advanced' },
      ],
      skills: [
        { name: 'Commercial Lending' },
        { name: 'Structured Finance' },
        { name: 'Bank Regulatory' },
        { name: 'Securities Offerings' },
        { name: 'Financial Services M&A' },
        { name: 'Derivatives' },
      ],
      industryExperience: [
        { name: 'Banking' },
        { name: 'Financial Services' },
        { name: 'Insurance' },
      ],
      professionalSummary:
        'Banking and finance attorney with expertise in complex financial transactions.',
      education: [
        { degree: 'J.D.', institution: 'SMU Dedman School of Law' },
        {
          degree: 'B.B.A. Finance',
          institution: 'University of Texas at Austin',
        },
      ],
      barAdmissions: [{ state: 'Texas' }],
      notableMatters: [
        {
          title: 'Bank acquisition',
          description: 'Advised on $2.5B bank acquisition',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available next month',
        matchPercentage: 89,
      },
    },
    {
      id: 12,
      name: 'Christopher Lee',
      initials: 'CL',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Denver',
      yearsExperience: 8,
      yearsWithFirm: 6,
      practiceAreas: [
        { name: 'Immigration Law', level: 'Expert' },
        { name: 'Employment Law', level: 'Intermediate' },
      ],
      skills: [
        { name: 'H-1B Visas' },
        { name: 'Green Card Applications' },
        { name: 'Corporate Immigration' },
        { name: 'Compliance Audits' },
        { name: 'Deportation Defense' },
        { name: 'Naturalization' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Healthcare' },
        { name: 'Manufacturing' },
      ],
      professionalSummary:
        'Immigration attorney specializing in corporate immigration and compliance.',
      education: [
        { degree: 'J.D.', institution: 'University of Colorado Law School' },
        {
          degree: 'B.A. International Relations',
          institution: 'University of Denver',
        },
      ],
      barAdmissions: [{ state: 'Colorado' }],
      notableMatters: [
        {
          title: 'Corporate immigration program',
          description:
            'Established immigration compliance program for tech company',
          year: '2023',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 86,
      },
    },
    {
      id: 13,
      name: 'Amanda Foster',
      initials: 'AF',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Phoenix',
      yearsExperience: 4,
      yearsWithFirm: 2,
      practiceAreas: [
        { name: 'Family Law', level: 'Advanced' },
        { name: 'Estate Planning', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Divorce Proceedings' },
        { name: 'Child Custody' },
        { name: 'Prenuptial Agreements' },
        { name: 'Wills and Trusts' },
        { name: 'Probate Administration' },
        { name: 'Guardianship' },
      ],
      industryExperience: [
        { name: 'High Net Worth Individuals' },
        { name: 'Entertainment' },
        { name: 'Sports' },
      ],
      professionalSummary:
        'Family law attorney with focus on high-asset divorce and estate planning.',
      education: [
        { degree: 'J.D.', institution: 'Arizona State University Law School' },
        { degree: 'B.A. Psychology', institution: 'University of Arizona' },
      ],
      barAdmissions: [{ state: 'Arizona' }],
      notableMatters: [
        {
          title: 'High-profile divorce case',
          description: 'Managed complex asset division for celebrity client',
          year: '2024',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 79,
      },
    },
    {
      id: 14,
      name: 'Kevin Brown',
      initials: 'KB',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Portland',
      yearsExperience: 16,
      yearsWithFirm: 13,
      practiceAreas: [
        { name: 'Antitrust Law', level: 'Expert' },
        { name: 'Competition Law', level: 'Expert' },
        { name: 'Regulatory Investigations', level: 'Advanced' },
      ],
      skills: [
        { name: 'Merger Review' },
        { name: 'Antitrust Litigation' },
        { name: 'Price Fixing Defense' },
        { name: 'Market Analysis' },
        { name: 'Competition Compliance' },
        { name: 'DOJ Investigations' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Telecommunications' },
        { name: 'Pharmaceuticals' },
        { name: 'Retail' },
      ],
      professionalSummary:
        'Antitrust attorney with extensive experience in merger review and competition law.',
      education: [
        { degree: 'J.D.', institution: 'Lewis & Clark Law School' },
        { degree: 'M.A. Economics', institution: 'University of Oregon' },
      ],
      barAdmissions: [{ state: 'Oregon' }, { state: 'Washington' }],
      notableMatters: [
        {
          title: 'Major merger clearance',
          description: 'Secured DOJ approval for $5B technology merger',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 94,
      },
    },
    {
      id: 15,
      name: 'Nicole Taylor',
      initials: 'NT',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Nashville',
      yearsExperience: 7,
      yearsWithFirm: 5,
      practiceAreas: [
        { name: 'Entertainment Law', level: 'Expert' },
        { name: 'Intellectual Property', level: 'Advanced' },
        { name: 'Media Law', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Music Contracts' },
        { name: 'Film and TV Agreements' },
        { name: 'Talent Representation' },
        { name: 'Copyright Law' },
        { name: 'Trademark Protection' },
        { name: 'Licensing Agreements' },
      ],
      industryExperience: [
        { name: 'Entertainment' },
        { name: 'Music' },
        { name: 'Film and Television' },
        { name: 'Publishing' },
      ],
      professionalSummary:
        'Entertainment attorney specializing in music and media transactions.',
      education: [
        { degree: 'J.D.', institution: 'Vanderbilt Law School' },
        { degree: 'B.A. Music Business', institution: 'Belmont University' },
      ],
      barAdmissions: [{ state: 'Tennessee' }, { state: 'California' }],
      notableMatters: [
        {
          title: 'Record label deal',
          description:
            'Negotiated major recording contract for Grammy-winning artist',
          year: '2024',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 87,
      },
    },
    {
      id: 16,
      name: 'Daniel Rodriguez',
      initials: 'DR',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Las Vegas',
      yearsExperience: 3,
      yearsWithFirm: 2,
      practiceAreas: [
        { name: 'Gaming Law', level: 'Advanced' },
        { name: 'Regulatory Compliance', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Gaming Licenses' },
        { name: 'Casino Compliance' },
        { name: 'Sports Betting' },
        { name: 'Online Gaming' },
        { name: 'Regulatory Investigations' },
        { name: 'Gaming Contracts' },
      ],
      industryExperience: [
        { name: 'Gaming and Hospitality' },
        { name: 'Entertainment' },
        { name: 'Technology' },
      ],
      professionalSummary:
        'Gaming law attorney specializing in casino operations and regulatory compliance.',
      education: [
        { degree: 'J.D.', institution: 'UNLV Boyd School of Law' },
        {
          degree: 'B.A. Business Administration',
          institution: 'University of Nevada, Las Vegas',
        },
      ],
      barAdmissions: [{ state: 'Nevada' }],
      notableMatters: [
        {
          title: 'Sports betting license',
          description:
            'Secured gaming license for major sports betting operator',
          year: '2024',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 81,
      },
    },
    {
      id: 17,
      name: 'Stephanie Clark',
      initials: 'SC',
      title: 'Senior Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Minneapolis',
      yearsExperience: 20,
      yearsWithFirm: 18,
      practiceAreas: [
        { name: 'White Collar Defense', level: 'Expert' },
        { name: 'Criminal Law', level: 'Expert' },
        { name: 'Government Investigations', level: 'Expert' },
      ],
      skills: [
        { name: 'Federal Criminal Defense' },
        { name: 'SEC Investigations' },
        { name: 'FCPA Compliance' },
        { name: 'Internal Investigations' },
        { name: 'Grand Jury Proceedings' },
        { name: 'Plea Negotiations' },
      ],
      industryExperience: [
        { name: 'Financial Services' },
        { name: 'Healthcare' },
        { name: 'Technology' },
        { name: 'Manufacturing' },
      ],
      professionalSummary:
        'White collar defense attorney with extensive experience in federal criminal matters.',
      education: [
        { degree: 'J.D.', institution: 'University of Minnesota Law School' },
        {
          degree: 'B.A. Criminal Justice',
          institution: 'University of Wisconsin',
        },
      ],
      barAdmissions: [{ state: 'Minnesota' }, { state: 'Wisconsin' }],
      notableMatters: [
        {
          title: 'High-profile fraud defense',
          description: 'Successfully defended CEO in major fraud case',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available next month',
        matchPercentage: 96,
      },
    },
    {
      id: 18,
      name: 'Brian Murphy',
      initials: 'BM',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Kansas City',
      yearsExperience: 5,
      yearsWithFirm: 3,
      practiceAreas: [
        { name: 'Agriculture Law', level: 'Advanced' },
        { name: 'Environmental Law', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Farm Transactions' },
        { name: 'Agricultural Compliance' },
        { name: 'Water Rights' },
        { name: 'Land Use Planning' },
        { name: 'Crop Insurance' },
        { name: 'Agricultural Contracts' },
      ],
      industryExperience: [
        { name: 'Agriculture' },
        { name: 'Food and Beverage' },
        { name: 'Rural Development' },
      ],
      professionalSummary:
        'Agriculture attorney specializing in farm operations and agricultural compliance.',
      education: [
        {
          degree: 'J.D.',
          institution: 'University of Missouri-Kansas City Law School',
        },
        {
          degree: 'B.S. Agricultural Economics',
          institution: 'Kansas State University',
        },
      ],
      barAdmissions: [{ state: 'Missouri' }, { state: 'Kansas' }],
      notableMatters: [
        {
          title: 'Large farm acquisition',
          description: 'Managed acquisition of 10,000-acre farming operation',
          year: '2023',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 77,
      },
    },
    {
      id: 19,
      name: 'Lauren Phillips',
      initials: 'LP',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Raleigh',
      yearsExperience: 13,
      yearsWithFirm: 9,
      practiceAreas: [
        { name: 'Privacy Law', level: 'Expert' },
        { name: 'Data Security', level: 'Expert' },
        { name: 'Technology Law', level: 'Advanced' },
      ],
      skills: [
        { name: 'GDPR Compliance' },
        { name: 'CCPA Implementation' },
        { name: 'Data Breach Response' },
        { name: 'Privacy Impact Assessments' },
        { name: 'Cybersecurity Law' },
        { name: 'Cross-border Data Transfers' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Healthcare' },
        { name: 'Financial Services' },
        { name: 'E-commerce' },
      ],
      professionalSummary:
        'Privacy and data security attorney with expertise in global privacy regulations.',
      education: [
        { degree: 'J.D.', institution: 'Duke University Law School' },
        {
          degree: 'B.S. Computer Science',
          institution: 'North Carolina State University',
        },
      ],
      barAdmissions: [{ state: 'North Carolina' }],
      notableMatters: [
        {
          title: 'Global privacy program implementation',
          description:
            'Designed comprehensive privacy program for multinational corporation',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 93,
      },
    },
    {
      id: 20,
      name: 'Gregory Adams',
      initials: 'GA',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Salt Lake City',
      yearsExperience: 10,
      yearsWithFirm: 8,
      practiceAreas: [
        { name: 'Mining Law', level: 'Expert' },
        { name: 'Natural Resources', level: 'Advanced' },
        { name: 'Environmental Law', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Mining Claims' },
        { name: 'Mineral Rights' },
        { name: 'Environmental Permitting' },
        { name: 'Public Lands Law' },
        { name: 'Water Rights' },
        { name: 'Reclamation Bonds' },
      ],
      industryExperience: [
        { name: 'Mining' },
        { name: 'Oil and Gas' },
        { name: 'Utilities' },
      ],
      professionalSummary:
        'Mining and natural resources attorney with expertise in western water and mineral law.',
      education: [
        {
          degree: 'J.D.',
          institution: 'University of Utah S.J. Quinney College of Law',
        },
        { degree: 'B.S. Geology', institution: 'University of Utah' },
      ],
      barAdmissions: [{ state: 'Utah' }, { state: 'Colorado' }],
      notableMatters: [
        {
          title: 'Major mining project permitting',
          description: 'Secured permits for $500M copper mining operation',
          year: '2024',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 85,
      },
    },
    {
      id: 21,
      name: 'Victoria Chen',
      initials: 'VC',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'San Francisco',
      yearsExperience: 12,
      yearsWithFirm: 8,
      practiceAreas: [
        { name: 'Venture Capital', level: 'Expert' },
        { name: 'Startup Law', level: 'Expert' },
        { name: 'Securities Law', level: 'Advanced' },
      ],
      skills: [
        { name: 'Seed Funding' },
        { name: 'Series A-C Rounds' },
        { name: 'Startup Formation' },
        { name: 'Stock Option Plans' },
        { name: 'Board Governance' },
        { name: 'Exit Strategies' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Fintech' },
        { name: 'Biotech' },
        { name: 'Clean Energy' },
      ],
      professionalSummary:
        'Venture capital attorney specializing in startup funding and technology transactions.',
      education: [
        { degree: 'J.D.', institution: 'Stanford Law School' },
        { degree: 'B.S. Computer Science', institution: 'UC Berkeley' },
      ],
      barAdmissions: [{ state: 'California' }],
      notableMatters: [
        {
          title: 'Unicorn startup Series C',
          description: 'Led $100M Series C funding for AI startup',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 91,
      },
    },
    {
      id: 22,
      name: 'Marcus Johnson',
      initials: 'MJ',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Detroit',
      yearsExperience: 9,
      yearsWithFirm: 6,
      practiceAreas: [
        { name: 'Automotive Law', level: 'Expert' },
        { name: 'Product Liability', level: 'Advanced' },
        { name: 'Regulatory Compliance', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Automotive Regulations' },
        { name: 'Safety Standards' },
        { name: 'Recall Management' },
        { name: 'Supplier Agreements' },
        { name: 'Autonomous Vehicle Law' },
        { name: 'Electric Vehicle Compliance' },
      ],
      industryExperience: [
        { name: 'Automotive' },
        { name: 'Manufacturing' },
        { name: 'Transportation' },
      ],
      professionalSummary:
        'Automotive attorney specializing in vehicle regulations and product liability.',
      education: [
        { degree: 'J.D.', institution: 'University of Michigan Law School' },
        {
          degree: 'B.S. Mechanical Engineering',
          institution: 'University of Michigan',
        },
      ],
      barAdmissions: [{ state: 'Michigan' }],
      notableMatters: [
        {
          title: 'Major vehicle recall',
          description: 'Managed legal aspects of 2M vehicle safety recall',
          year: '2024',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 88,
      },
    },
    {
      id: 23,
      name: 'Sarah Williams',
      initials: 'SW',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'New Orleans',
      yearsExperience: 6,
      yearsWithFirm: 4,
      practiceAreas: [
        { name: 'Maritime Law', level: 'Advanced' },
        { name: 'International Trade', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Admiralty Law' },
        { name: 'Shipping Contracts' },
        { name: 'Port Operations' },
        { name: 'Marine Insurance' },
        { name: 'Cargo Claims' },
        { name: 'Jones Act' },
      ],
      industryExperience: [
        { name: 'Shipping' },
        { name: 'Oil and Gas' },
        { name: 'International Trade' },
      ],
      professionalSummary:
        'Maritime attorney specializing in admiralty law and international shipping.',
      education: [
        { degree: 'J.D.', institution: 'Tulane University Law School' },
        {
          degree: 'B.A. International Business',
          institution: 'Tulane University',
        },
      ],
      barAdmissions: [{ state: 'Louisiana' }],
      notableMatters: [
        {
          title: 'International shipping dispute',
          description:
            'Resolved complex cargo claim involving multiple jurisdictions',
          year: '2023',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available next month',
        matchPercentage: 82,
      },
    },
    {
      id: 24,
      name: 'Thomas Anderson',
      initials: 'TA',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Anchorage',
      yearsExperience: 17,
      yearsWithFirm: 14,
      practiceAreas: [
        { name: 'Energy Law', level: 'Expert' },
        { name: 'Natural Resources', level: 'Expert' },
        { name: 'Environmental Law', level: 'Advanced' },
      ],
      skills: [
        { name: 'Oil and Gas Law' },
        { name: 'Pipeline Regulations' },
        { name: 'Arctic Development' },
        { name: 'Native Corporation Law' },
        { name: 'Environmental Impact' },
        { name: 'Resource Extraction' },
      ],
      industryExperience: [
        { name: 'Oil and Gas' },
        { name: 'Mining' },
        { name: 'Utilities' },
        { name: 'Native Corporations' },
      ],
      professionalSummary:
        'Energy and natural resources attorney with expertise in Arctic development.',
      education: [
        { degree: 'J.D.', institution: 'University of Alaska Anchorage' },
        {
          degree: 'B.S. Petroleum Engineering',
          institution: 'University of Alaska Fairbanks',
        },
      ],
      barAdmissions: [{ state: 'Alaska' }],
      notableMatters: [
        {
          title: 'Arctic oil development project',
          description: 'Led permitting for major Arctic oil exploration',
          year: '2024',
          role: 'Lead Partner',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 90,
      },
    },
    {
      id: 25,
      name: 'Elena Rodriguez',
      initials: 'ER',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Honolulu',
      yearsExperience: 8,
      yearsWithFirm: 5,
      practiceAreas: [
        { name: 'Tourism Law', level: 'Expert' },
        { name: 'Hospitality Law', level: 'Advanced' },
        { name: 'Real Estate', level: 'Intermediate' },
      ],
      skills: [
        { name: 'Hotel Development' },
        { name: 'Resort Operations' },
        { name: 'Tourism Regulations' },
        { name: 'Vacation Rental Law' },
        { name: 'Timeshare Law' },
        { name: 'Land Use Planning' },
      ],
      industryExperience: [
        { name: 'Hospitality' },
        { name: 'Tourism' },
        { name: 'Real Estate' },
        { name: 'Entertainment' },
      ],
      professionalSummary:
        'Tourism and hospitality attorney specializing in resort development and operations.',
      education: [
        { degree: 'J.D.', institution: 'University of Hawaii Law School' },
        {
          degree: 'B.A. Hospitality Management',
          institution: 'University of Hawaii',
        },
      ],
      barAdmissions: [{ state: 'Hawaii' }],
      notableMatters: [
        {
          title: 'Luxury resort development',
          description: 'Managed legal aspects of $500M resort project',
          year: '2024',
          role: 'Senior Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 84,
      },
    },
  ];

  constructor() {}

  getAllProfiles(): Observable<ProfileData[]> {
    return of(this.profiles);
  }

  getProfileById(id: number): Observable<ProfileData | undefined> {
    return of(this.profiles.find((profile) => profile.id === +id));
  }

  searchProfiles(
    query: string = '',
    experienceLevel?: string,
    location?: string,
    language?: string,
    availability?: string
  ): Observable<ProfileData[]> {
    let filteredProfiles = this.profiles;

    // Filter by search query
    if (query) {
      const lowerQuery = query.toLowerCase();
      filteredProfiles = filteredProfiles.filter(
        (profile) =>
          profile.name.toLowerCase().includes(lowerQuery) ||
          profile.title.toLowerCase().includes(lowerQuery) ||
          profile.practiceAreas.some((area) =>
            area.name.toLowerCase().includes(lowerQuery)
          ) ||
          profile.skills.some((skill) =>
            skill.name.toLowerCase().includes(lowerQuery)
          )
      );
    }

    // Filter by experience level
    if (experienceLevel) {
      filteredProfiles = filteredProfiles.filter((profile) => {
        if (experienceLevel === 'Junior (1-3 years)') {
          return profile.yearsExperience >= 1 && profile.yearsExperience <= 3;
        } else if (experienceLevel === 'Mid-level (4-6 years)') {
          return profile.yearsExperience >= 4 && profile.yearsExperience <= 6;
        } else if (experienceLevel === 'Senior (7-10 years)') {
          return profile.yearsExperience >= 7 && profile.yearsExperience <= 10;
        } else if (experienceLevel === 'Partner (10+ years)') {
          return profile.yearsExperience > 10;
        }
        return true;
      });
    }

    // Filter by location
    if (location) {
      filteredProfiles = filteredProfiles.filter((profile) =>
        profile.location.includes(location)
      );
    }

    // Filter by availability
    if (availability) {
      filteredProfiles = filteredProfiles.filter(
        (profile) => profile.availability.status === availability
      );
    }

    return of(filteredProfiles);
  }

  getSearchSuggestions(query: string): Observable<any[]> {
    if (!query || query.length < 1) {
      return of([]);
    }

    const suggestions: any[] = [];
    const lowerQuery = query.toLowerCase();

    // Find matching profiles
    const matchingProfiles = this.profiles
      .filter(
        (profile) =>
          profile.name.toLowerCase().includes(lowerQuery) ||
          profile.title.toLowerCase().includes(lowerQuery) ||
          profile.skills.some((skill) =>
            skill.name.toLowerCase().includes(lowerQuery)
          ) ||
          profile.practiceAreas.some((area) =>
            area.name.toLowerCase().includes(lowerQuery)
          )
      )
      .slice(0, 5);

    // Add profiles to suggestions
    matchingProfiles.forEach((profile) => {
      suggestions.push({
        type: 'profile',
        name: profile.name,
        title: profile.title,
        email: profile.email,
        location: profile.location,
        initials: profile.initials,
        profileId: profile.id,
      });
    });

    return of(suggestions);
  }

  getSkillSuggestions(query: string): Observable<any[]> {
    if (!query || query.length < 1) {
      return of([]);
    }

    const lowerQuery = query.toLowerCase();
    const skillCounts = new Map<string, number>();

    // Count associates for each skill
    this.profiles.forEach((profile) => {
      profile.skills.forEach((skill) => {
        if (skill.name.toLowerCase().includes(lowerQuery)) {
          skillCounts.set(skill.name, (skillCounts.get(skill.name) || 0) + 1);
        }
      });
      profile.practiceAreas.forEach((area) => {
        if (area.name.toLowerCase().includes(lowerQuery)) {
          skillCounts.set(area.name, (skillCounts.get(area.name) || 0) + 1);
        }
      });
    });

    // Convert to array with counts and categories
    const skillSuggestions = Array.from(skillCounts.entries())
      .map(([skillName, count]) => ({
        name: skillName,
        associateCount: count,
        category: this.getSkillCategory(skillName),
      }))
      .sort((a, b) => b.associateCount - a.associateCount)
      .slice(0, 8);

    return of(skillSuggestions);
  }

  private getSkillCategory(skillName: string): string {
    // Comprehensive categorization based on skill name to match color mapping
    const skillLower = skillName.toLowerCase();

    // Corporate/Business Law (Purple)
    const corporateSkills = [
      'corporate law',
      'mergers & acquisitions',
      'private equity',
      'venture capital',
      'ipos',
      'securities regulation',
      'm&a',
      'due diligence',
      'joint ventures',
      'corporate restructuring',
      'debt financing',
      'asset sales',
      'stock purchase',
      'deal structuring',
      'strategic alliances',
      'corporate governance',
      'securities offerings',
    ];

    // Litigation/Dispute Resolution (Blue)
    const litigationSkills = [
      'litigation',
      'commercial litigation',
      'securities litigation',
      'antitrust litigation',
      'trial advocacy',
      'class action defense',
      'white collar defense',
      'internal investigations',
      'arbitration',
      'mediation',
      'appellate practice',
      'dispute resolution',
      'price fixing defense',
    ];

    // Regulatory/Compliance (Green)
    const regulatorySkills = [
      'regulatory',
      'compliance',
      'regulatory enforcement',
      'regulatory investigations',
      'hipaa compliance',
      'fda regulations',
      'compliance audits',
      'competition compliance',
      'doj investigations',
      'gaming compliance',
      'casino compliance',
      'regulatory compliance',
    ];

    // Finance/Tax Law (Orange)
    const financeSkills = [
      'finance',
      'tax',
      'corporate finance',
      'financial services',
      'banking',
      'investment',
      'securities',
      'capital markets',
      'debt',
      'equity',
    ];

    // Employment/Labor Law (Red)
    const employmentSkills = [
      'employment',
      'labor',
      'immigration',
      'h-1b visas',
      'green card applications',
      'corporate immigration',
      'deportation defense',
      'naturalization',
    ];

    // Real Estate/Property Law (Teal)
    const realEstateSkills = [
      'real estate',
      'property',
      'land use',
      'hotel development',
      'resort operations',
      'vacation rental',
      'timeshare',
      'land use planning',
    ];

    // Intellectual Property (IP category maps to blue in component)
    const ipSkills = [
      'patent',
      'trademark',
      'copyright',
      'intellectual property',
      'ip due diligence',
      'technology licensing',
      'software licensing',
      'open source compliance',
      'trade secrets',
      'ip portfolio',
      'ip strategy',
    ];

    // Check categories in order of specificity
    if (regulatorySkills.some((skill) => skillLower.includes(skill)))
      return 'Regulatory';
    if (financeSkills.some((skill) => skillLower.includes(skill)))
      return 'Finance';
    if (employmentSkills.some((skill) => skillLower.includes(skill)))
      return 'Employment';
    if (realEstateSkills.some((skill) => skillLower.includes(skill)))
      return 'Real Estate';
    if (litigationSkills.some((skill) => skillLower.includes(skill)))
      return 'Litigation';
    if (ipSkills.some((skill) => skillLower.includes(skill))) return 'IP';
    if (corporateSkills.some((skill) => skillLower.includes(skill)))
      return 'Corporate';

    return 'Corporate'; // Default category
  }
}
