# Skills Management Application

A modern Angular-based web application for managing and searching professional profiles and skills. This application is designed for law firms and professional services organizations to efficiently manage their talent pool and find the right professionals for specific projects.

## 🚀 Features

### Core Functionality

- **Profile Management**: Comprehensive professional profiles with detailed information
- **Advanced Search**: Multi-criteria search with filters for experience, location, availability, and more
- **Authentication System**: Secure login/logout functionality with route guards
- **Responsive Design**: Modern UI built with Angular Material and Bootstrap
- **Dashboard**: Overview of key metrics and quick access to main features

### Profile Features

- **Professional Information**: Name, title, contact details, experience years
- **Practice Areas**: Specialized legal practice areas with expertise levels
- **Skills**: Comprehensive skill sets and competencies
- **Industry Experience**: Sector-specific experience
- **Education & Bar Admissions**: Professional qualifications and certifications
- **Notable Matters**: Key projects and achievements
- **Availability Status**: Real-time availability tracking with match percentages

### Search & Filtering

- **Text Search**: Search across all profile fields
- **Experience Level Filtering**: Junior, Mid-level, Senior, Partner
- **Location-based Search**: Filter by geographic location
- **Language Filtering**: Multi-language support
- **Availability Filtering**: Current, next week, next month availability
- **Date-based Filtering**: Advanced date picker for availability

## 🛠️ Technology Stack

### Frontend Framework

- **Angular 16.2.0**: Modern TypeScript-based framework
- **Angular Material 16.2.14**: Material Design components
- **Bootstrap 5.3.2**: Responsive CSS framework
- **NgBootstrap 15.1.2**: Bootstrap components for Angular

### UI/UX Libraries

- **Chart.js 4.4.0**: Data visualization
- **Ng2-charts 5.0.3**: Angular wrapper for Chart.js
- **Lucide Angular 0.507.0**: Modern icon library
- **Tabler Icons 3.31.0**: Additional icon set
- **Bootstrap Icons 1.11.2**: Bootstrap icon library

### Form & Input Components

- **NgSelect 11**: Advanced select dropdowns
- **Ngx-slider-v2 17.0.0**: Range slider component
- **Reactive Forms**: Angular's reactive form system

### Utilities

- **Moment.js 2.29.4**: Date manipulation
- **XLSX 0.18.5**: Excel file handling
- **html2canvas 1.4.1**: HTML to canvas conversion
- **jsPDF 3.0.1**: PDF generation
- **UUID4 2.0.3**: Unique identifier generation

### Development Tools

- **TypeScript 5.1.3**: Type-safe JavaScript
- **ESLint 8.51.0**: Code linting
- **Angular CLI 16.2.12**: Development and build tools

## 📁 Project Structure

```
src/
├── app/
│   ├── core/                    # Core application logic
│   │   ├── auth/               # Authentication services
│   │   ├── models/             # Data models and interfaces
│   │   ├── services/           # Core services
│   │   └── theme/              # Theme configuration
│   ├── features/               # Feature modules
│   ├── layout/                 # Layout components
│   ├── screens/                # Main application screens
│   │   ├── auth/              # Authentication screens
│   │   ├── dashboard/         # Dashboard screen
│   │   ├── profile/           # Profile management screens
│   │   └── not-found/         # 404 error page
│   └── shared/                # Shared components and modules
│       └── components/        # Reusable UI components
├── assets/                    # Static assets
└── styles.css                # Global styles
```

## 🚀 Getting Started

### Prerequisites

- **Node.js 20.x**: Required for Angular development
- **Yarn**: Package manager (recommended) or npm
- **Angular CLI**: Global installation recommended

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd skills
   ```

2. **Install dependencies**

   ```bash
   yarn install
   # or
   npm install
   ```

3. **Start the development server**

   ```bash
   yarn start
   # or
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:4200`

### Build Commands

```bash
# Development build
yarn build

# Production build
yarn build --configuration production

# Watch mode for development
yarn watch

# Run tests
yarn test
```

## 🔧 Configuration

### Environment Setup

The application uses Angular's environment system. Create environment files in `src/environments/`:

- `environment.ts` - Development environment
- `environment.prod.ts` - Production environment

### Authentication

Currently uses a mock authentication service. In production, integrate with:

- Azure AD (MSAL is already included)
- Custom authentication backend
- OAuth providers

## 📊 Data Models & Services

### Profile Data Structure

```typescript
interface ProfileData {
  id: number;
  name: string;
  initials: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  yearsExperience: number;
  yearsWithFirm?: number;
  avatarUrl?: string;
  practiceAreas: PracticeArea[];
  skills: Skill[];
  industryExperience: IndustryExperience[];
  professionalSummary: string;
  education: Education[];
  barAdmissions: BarAdmission[];
  notableMatters: NotableMatter[];
  availability: Availability;
}

interface PracticeArea {
  name: string;
  level: "Expert" | "Advanced" | "Intermediate" | "Beginner";
}

interface Skill {
  name: string;
}

interface Availability {
  status: "Available now" | "Available next week" | "Available next month";
  matchPercentage?: number;
}
```

### ProfileService Methods

**Location:** `src/app/core/services/profile/profile.service.ts`

The ProfileService provides comprehensive data access and search functionality:

```typescript
export class ProfileService {
  // Core data methods
  getAllProfiles(): Observable<ProfileData[]>;
  getProfileById(id: number): Observable<ProfileData | undefined>;

  // Advanced search with multiple filters
  searchProfiles(query?: string, experienceLevel?: string, location?: string, language?: string, availability?: string): Observable<ProfileData[]>;

  // Autocomplete suggestions for profiles
  getSearchSuggestions(query: string): Observable<any[]>;

  // Skill-based autocomplete with counts
  getSkillSuggestions(query: string): Observable<any[]>;
}
```

**Search Suggestions Response:**

```typescript
// Profile suggestions include
{
  type: 'profile',
  profileId: number,
  name: string,
  initials: string,
  email: string,
  title: string,
  location: string
}

// Skill suggestions include
{
  name: string,
  associateCount: number,
  category: 'Corporate' | 'Litigation' | 'IP' | 'Real Estate'
}

// Skill categories are automatically mapped based on skill names:
// - Corporate/Business → Purple background
// - Litigation/Dispute → Blue background
// - Regulatory/Compliance → Green background
// - Finance/Tax → Orange background
// - Employment/Labor → Red background
// - Real Estate/Property → Teal background
```

## 🎨 UI Components

### Shared Components

- **Icon Component**: Unified icon system
- **Date Picker**: Advanced date selection
- **Lucide Icon**: Modern icon integration

### Layout Components

- **Layout Component**: Main application layout
- **Navigation**: Responsive navigation system

## 📚 Component Documentation

### 1. Profile List Component (`ProfileListComponent`)

**Location:** `src/app/screens/profile/profile-list/`

**Purpose:**
Advanced searchable and filterable list of professional profiles with intelligent autocomplete, skill-based filtering, infinite scroll pagination, and multiple display styles. The component provides a comprehensive interface for finding and browsing legal professionals.

**Key Files:**

- `profile-list.component.ts` – Component logic (663 lines) with autocomplete, filtering, pagination
- `profile-list.component.html` – UI template (570 lines) with search interface and profile cards
- `profile-list.component.css` – Styles for autocomplete, cards, and responsive design

**Core Features:**

🔍 **Intelligent Search & Autocomplete:**

- Real-time search suggestions with debounced input (300ms)
- Dual autocomplete: Skills and Associate profiles in unified dropdown
- Keyboard navigation (Arrow keys, Enter, Escape)
- Limited suggestions display (3 skills, 5 profiles) with "View All" option
- Search across names, titles, practice areas, and skills

🏷️ **Advanced Skill Filtering:**

- Multi-select skill filters with visual chips
- Skill suggestions with associate counts and categories
- Smart matching for M&A variations (merger, acquisition, m&a)
- Skill icons with color-coded categories and intelligent mapping:
  - 🟣 **Purple**: Corporate, Business Law (default)
  - 🔵 **Blue**: Litigation, Dispute Resolution
  - 🟢 **Green**: Regulatory, Compliance
  - 🟠 **Orange**: Finance, Tax Law
  - 🔴 **Red**: Employment, Labor Law
  - 🟡 **Teal**: Real Estate, Property Law
- Selected skills remain in search input for context

📊 **Smart Filtering System:**

- Experience level filtering (Junior, Mid-level, Senior, Partner)
- Location-based filtering (New York, San Francisco, Chicago, Los Angeles)
- Language support filtering (English, Spanish, Mandarin, French)
- Availability filtering with date picker integration
- Dynamic filter state management with clear filters option

📅 **Date Picker Integration:**

- Custom date picker component for availability selection
- Automatic availability categorization based on selected dates
- Support for both single date and date range selection
- Smart date-to-availability mapping (now, next week, next month)

♾️ **Infinite Scroll Pagination:**

- Load 10 profiles initially, then load more on scroll
- Smooth loading indicators and "Load More" fallback button
- Efficient memory management with displayed vs. all profiles separation
- Scroll position detection with 100px buffer

🎨 **Flexible Display Options:**

- Two card styles: Compact list view (Style 1) and detailed grid view (Style 2)
- Responsive design with Bootstrap grid system
- Profile cards with avatar initials, experience indicators, and match percentages
- Hover effects and smooth transitions

**How to Use:**

```typescript
// Add to your module imports
import { ProfileListComponent } from './screens/profile/profile-list/profile-list.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [ProfileListComponent],
  imports: [NgSelectModule, FormsModule, ...],
})
export class YourModule {}
```

```html
<!-- Use in your template -->
<app-profile-list></app-profile-list>
```

**Advanced Usage Examples:**

```typescript
// Component with all advanced features
export class ProfileListComponent implements OnInit, OnDestroy {
  // Autocomplete properties
  searchSuggestions: any[] = [];
  showSuggestions = false;
  selectedSuggestionIndex = -1;
  private searchSubject = new Subject<string>();

  // Skill filtering
  selectedSkills: string[] = [];
  skillSuggestions: any[] = [];
  showSkillSuggestions = false;

  // Infinite scroll
  displayedProfiles: ProfileData[] = [];
  allProfiles: ProfileData[] = [];
  profilesPerPage = 10;
  hasMoreProfiles = true;

  // Setup autocomplete with debouncing
  setupAutocomplete(): void {
    this.searchSubject
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((query) => this.profileService.getSearchSuggestions(query)),
        takeUntil(this.destroy$)
      )
      .subscribe((suggestions) => {
        this.searchSuggestions = suggestions;
        this.showSuggestions = suggestions.length > 0;
      });
  }

  // Handle keyboard navigation
  onSearchKeydown(event: KeyboardEvent): void {
    switch (event.key) {
      case "ArrowDown":
        this.selectedSuggestionIndex = Math.min(this.selectedSuggestionIndex + 1, this.totalSuggestions - 1);
        break;
      case "Enter":
        if (this.selectedSuggestionIndex >= 0) {
          this.selectSuggestion();
        }
        break;
    }
  }

  // Infinite scroll implementation
  onScroll(): void {
    const element = document.documentElement;
    const atBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + 100;

    if (atBottom && this.hasMoreProfiles && !this.isLoadingMore) {
      this.loadMoreProfiles();
    }
  }
}
```

### 2. Profile Page Component (`ProfileComponent`)

**Location:** `src/app/screens/profile/profile/`

**Purpose:**  
Displays detailed information for a single professional profile, including practice areas, skills, education, bar admissions, notable matters, and contact info.

**Key Files:**

- `profile.component.ts` – Fetches profile by ID, handles loading/error
- `profile.component.html` – UI template for all profile sections

**How to Use:**

```typescript
// Add to your module
import { ProfileComponent } from './screens/profile/profile/profile.component';

@NgModule({
  declarations: [ProfileComponent, ...],
  ...
})
export class YourModule {}
```

```html
<!-- Use in your template, expects route param 'id' -->
<app-profile></app-profile>
```

**Features:**

- Loads profile by route param (`/profile/:id`)
- Shows all profile details, including practice areas, skills, education, bar admissions, notable matters, and contact info
- Loading and error states
- Back navigation

**Copy-Paste Usage:**

```typescript
// profile.component.ts
import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ProfileService } from "../../../core/services/profile/profile.service";
import { ProfileData } from "../../../core/models/profile.model";

@Component({
  selector: "app-profile",
  templateUrl: "./profile.component.html",
  styleUrls: ["./profile.component.css"],
})
export class ProfileComponent implements OnInit {
  profileData?: ProfileData;
  loading = true;
  error = false;

  constructor(private route: ActivatedRoute, private router: Router, private profileService: ProfileService) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get("id");
      if (id) {
        this.loadProfile(+id);
      } else {
        this.router.navigate(["/profile"]);
      }
    });
  }

  loadProfile(id: number): void {
    this.loading = true;
    this.error = false;

    this.profileService.getProfileById(id).subscribe({
      next: (profile) => {
        if (profile) {
          this.profileData = profile;
          this.loading = false;
        } else {
          this.error = true;
          this.loading = false;
        }
      },
      error: (err) => {
        console.error("Error loading profile", err);
        this.error = true;
        this.loading = false;
      },
    });
  }
}
```

### 3. Shared Components

#### a. Icon Component (`IconComponent`)

**Location:** `src/app/shared/components/icon/`

**Purpose:**  
Reusable SVG icon component supporting multiple icon types.

**How to Use:**

```typescript
import { IconComponent } from 'src/app/shared/components/icon/icon.component';

@Component({
  ...
  imports: [IconComponent]
})
```

```html
<app-icon name="info-circle" [size]="20"></app-icon>
```

**Supported icons:** info-circle, map-pin, briefcase, book, calendar, chevron-down, chevron-left, chevron-right, alert-circle, clock, mail, phone, arrow-right, arrow-left, twitter, linkedin, user

**Copy-Paste Usage:**

```typescript
// icon.component.ts
import { Component, Input, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DomSanitizer, SafeHtml } from "@angular/platform-browser";

@Component({
  selector: "app-icon",
  template: `<span [innerHTML]="svgContent"></span>`,
  standalone: true,
  imports: [CommonModule],
})
export class IconComponent implements OnInit {
  @Input() name!: string;
  @Input() size: number = 16;

  svgContent: SafeHtml = "";

  constructor(private sanitizer: DomSanitizer) {}

  ngOnInit(): void {
    this.updateIcon();
  }

  private updateIcon(): void {
    let svg = "";

    switch (this.name) {
      case "info-circle":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>`;
        break;
      case "map-pin":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>`;
        break;
      case "briefcase":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg>`;
        break;
      case "book":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/></svg>`;
        break;
      case "calendar":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 2v3"/><path d="M16 2v3"/><path d="M3 10h18"/><path d="M3 5h18v16H3z"/></svg>`;
        break;
      case "chevron-down":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>`;
        break;
      case "chevron-left":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>`;
        break;
      case "chevron-right":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>`;
        break;
      case "alert-circle":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>`;
        break;
      case "clock":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>`;
        break;
      case "mail":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>`;
        break;
      case "phone":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>`;
        break;
      case "arrow-right":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>`;
        break;
      case "arrow-left":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5"/><path d="m12 19-7-7 7-7"/></svg>`;
        break;
      case "twitter":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"/></svg>`;
        break;
      case "linkedin":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/><rect width="4" height="12" x="2" y="9"/><circle cx="4" cy="4" r="2"/></svg>`;
        break;
      case "user":
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>`;
        break;
      default:
        svg = "";
    }

    this.svgContent = this.sanitizer.bypassSecurityTrustHtml(svg);
  }
}
```

#### b. Date Picker Component (`DatePickerComponent`)

**Location:** `src/app/shared/components/date-picker/`

**Purpose:**  
Reusable date picker supporting single date and range selection.

**How to Use:**

```typescript
import { DatePickerComponent } from 'src/app/shared/components/date-picker/date-picker.component';

@Component({
  ...
  imports: [DatePickerComponent]
})
```

```html
<app-date-picker placeholder="Pick availability date" [initialSelection]="{ type: 'single', singleDate: null }" (dateSelected)="onDateSelectionChange($event)"> </app-date-picker>
```

**Copy-Paste Usage:**

```typescript
// date-picker.component.ts (simplified version)
import { Component, Input, Output, EventEmitter, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

export interface DateSelection {
  type: "single" | "range";
  singleDate?: string | null;
  startDate?: string | null;
  endDate?: string | null;
}

@Component({
  selector: "app-date-picker",
  template: `
    <div class="date-picker-container">
      <input type="text" [placeholder]="placeholder" [value]="getDisplayText()" (click)="toggle()" readonly class="form-control" />
      <div *ngIf="isOpen" class="date-picker-dropdown">
        <!-- Calendar implementation -->
        <div class="calendar-header">
          <button (click)="prevMonth()">&lt;</button>
          <span>{{ getMonthYearText() }}</span>
          <button (click)="nextMonth()">&gt;</button>
        </div>
        <div class="calendar-body">
          <!-- Calendar days implementation -->
        </div>
        <div class="calendar-footer">
          <button (click)="apply()">Apply</button>
          <button (click)="cancel()">Cancel</button>
        </div>
      </div>
    </div>
  `,
  standalone: true,
  imports: [CommonModule, FormsModule],
})
export class DatePickerComponent implements OnInit {
  @Input() placeholder = "Pick a date";
  @Input() initialSelection: DateSelection | null = null;
  @Output() dateSelected = new EventEmitter<DateSelection>();

  isOpen = false;
  mode: "single" | "range" = "single";
  singleDate: string | null = null;
  rangeStartDate: string | null = null;
  rangeEndDate: string | null = null;

  ngOnInit(): void {
    if (this.initialSelection) {
      this.mode = this.initialSelection.type;
      if (this.initialSelection.type === "single") {
        this.singleDate = this.initialSelection.singleDate || null;
      } else {
        this.rangeStartDate = this.initialSelection.startDate || null;
        this.rangeEndDate = this.initialSelection.endDate || null;
      }
    }
  }

  toggle(): void {
    this.isOpen = !this.isOpen;
  }

  getDisplayText(): string {
    if (this.mode === "single" && this.singleDate) {
      return this.formatDate(this.singleDate);
    } else if (this.mode === "range" && this.rangeStartDate && this.rangeEndDate) {
      return `${this.formatDate(this.rangeStartDate)} - ${this.formatDate(this.rangeEndDate)}`;
    }
    return "";
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }

  apply(): void {
    const selection: DateSelection = {
      type: this.mode,
      singleDate: this.singleDate,
      startDate: this.rangeStartDate,
      endDate: this.rangeEndDate,
    };
    this.dateSelected.emit(selection);
    this.isOpen = false;
  }

  cancel(): void {
    this.isOpen = false;
  }

  // Additional methods for calendar navigation and date selection
  prevMonth(): void {
    /* Implementation */
  }
  nextMonth(): void {
    /* Implementation */
  }
  getMonthYearText(): string {
    /* Implementation */
  }
}
```

## 🚀 Profile List Page Usage Guide

### Quick Start

The Profile List page (`/profile`) is the main interface for browsing and searching professional profiles. It's accessible through the navigation menu and provides comprehensive search and filtering capabilities.

### User Interface Overview

**Search Interface:**

- **Main Search Bar**: Type to search across names, titles, practice areas, and skills
- **Autocomplete Dropdown**: Shows both skill suggestions and profile matches
- **Filter Row**: Experience level, location, language, and availability date filters
- **Action Buttons**: Search and Clear filters buttons

**Results Display:**

- **Results Counter**: Shows total number of associates found
- **Profile Cards**: Compact cards with key information and match percentages
- **Infinite Scroll**: Automatically loads more profiles as you scroll
- **Load More Button**: Fallback option for manual loading

### How to Use the Profile List

#### 1. Basic Search

```
1. Navigate to /profile or click "Find Associates" in navigation
2. Type in the search bar (e.g., "corporate law", "Sarah", "M&A")
3. View real-time suggestions in the dropdown
4. Click on a suggestion or press Enter to search
```

#### 2. Advanced Filtering

```
1. Use the filter dropdowns below the search bar:
   - Experience Level: Junior, Mid-level, Senior, Partner
   - Office Location: New York, San Francisco, Chicago, Los Angeles
   - Languages: English, Spanish, Mandarin, French
   - Availability Date: Use the date picker for specific dates

2. Click "Search" to apply filters
3. Use "Clear" to reset all filters and show all profiles
```

#### 3. Skill-Based Search

```
1. Type a skill name in the search bar (e.g., "litigation", "tax")
2. Select from skill suggestions in the dropdown
3. The selected skill appears in the search input
4. Results are filtered to show only profiles with that skill
5. Remove skills by clearing the search input
```

#### 4. Autocomplete Navigation

```
Keyboard shortcuts in the autocomplete dropdown:
- ↑/↓ Arrow keys: Navigate through suggestions
- Enter: Select highlighted suggestion
- Escape: Close dropdown
- Mouse hover: Highlight suggestions
```

### Integration Examples

#### Adding to Your Angular App

```typescript
// app-routing.module.ts
const routes: Routes = [
  {
    path: "profile",
    loadChildren: () => import("./screens/profile/profile.module").then((m) => m.ProfileModule),
  },
];

// profile-routing.module.ts
const routes: Routes = [
  { path: "", component: ProfileListComponent },
  { path: ":id", component: ProfileComponent },
];
```

#### Custom Service Integration

```typescript
// Replace ProfileService with your own data source
export class CustomProfileService {
  searchProfiles(query: string, filters: any): Observable<ProfileData[]> {
    // Your API call here
    return this.http.get<ProfileData[]>("/api/profiles/search", {
      params: { query, ...filters },
    });
  }

  getSearchSuggestions(query: string): Observable<any[]> {
    // Your autocomplete API
    return this.http.get<any[]>("/api/profiles/suggestions", {
      params: { q: query },
    });
  }
}
```

### Required Dependencies

```json
{
  "dependencies": {
    "@ng-select/ng-select": "^11.0.0",
    "bootstrap": "^5.3.2",
    "bootstrap-icons": "^1.11.2",
    "rxjs": "^7.8.0"
  }
}
```

### Module Setup

```typescript
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { NgSelectModule } from "@ng-select/ng-select";

import { ProfileListComponent } from "./screens/profile/profile-list/profile-list.component";
import { ProfileComponent } from "./screens/profile/profile/profile.component";
import { IconComponent } from "./shared/components/icon/icon.component";
import { DatePickerComponent } from "./shared/components/date-picker/date-picker.component";

@NgModule({
  declarations: [ProfileListComponent, ProfileComponent],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgSelectModule,
    IconComponent, // Standalone component
    DatePickerComponent, // Standalone component
  ],
  exports: [ProfileListComponent, ProfileComponent],
})
export class ProfileModule {}
```

### Performance Considerations

- **Debounced Search**: 300ms delay prevents excessive API calls
- **Infinite Scroll**: Loads 10 profiles at a time for better performance
- **Memory Management**: Separates displayed profiles from all profiles
- **Suggestion Limits**: Shows max 3 skills and 5 profiles in dropdown
- **Lazy Loading**: Profile module is lazy-loaded for faster initial load
